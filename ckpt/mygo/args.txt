lr	<class 'float'>
dim	<class 'int'>
valid_epoch	<class 'int'>
num_layer_enc_ent	<class 'int'>
num_layer_enc_rel	<class 'int'>
num_layer_dec	<class 'int'>
num_head	<class 'int'>
hidden_dim	<class 'int'>
dropout	<class 'float'>
emb_dropout	<class 'float'>
vis_dropout	<class 'float'>
txt_dropout	<class 'float'>
smoothing	<class 'float'>
batch_size	<class 'int'>
decay	<class 'float'>
max_img_num	<class 'int'>
step_size	<class 'int'>
max_vis_token	<class 'int'>
max_txt_token	<class 'int'>
score_function	<class 'str'>
mu	<class 'float'>
